import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'network_client.dart';

/// Google API诊断工具
class GoogleApiDiagnostics {
  /// 诊断Google API连接问题
  static Future<Map<String, dynamic>> diagnoseConnection({
    required String apiKey,
    String? model,
    String? proxyHost,
    int? proxyPort,
  }) async {
    final results = <String, dynamic>{
      'success': false,
      'issues': <String>[],
      'recommendations': <String>[],
      'testResults': <Map<String, dynamic>>[],
      'workingConfig': null,
    };

    // 1. 验证API密钥格式
    if (!_isValidApiKeyFormat(apiKey)) {
      results['issues'].add('API密钥格式不正确');
      results['recommendations'].add('请检查API密钥是否完整，通常以AIza开头');
      return results;
    }

    // 2. 测试网络连接
    final networkTest = await _testNetworkConnectivity();
    results['testResults'].add(networkTest);

    if (!networkTest['canReachGoogle']) {
      results['issues'].add('无法访问Google服务');
      results['recommendations'].add('需要使用代理或VPN访问Google API');
    }

    // 3. 测试不同的连接方式
    final connectionTests = await _testConnectionMethods(
      apiKey: apiKey,
      model: model ?? 'gemini-1.5-flash',
      proxyHost: proxyHost,
      proxyPort: proxyPort,
    );

    results['testResults'].addAll(connectionTests);

    // 4. 分析结果并提供建议
    final analysis = _analyzeResults(connectionTests);
    results.addAll(analysis);

    return results;
  }

  /// 验证API密钥格式
  static bool _isValidApiKeyFormat(String apiKey) {
    // Google API密钥通常以AIza开头，长度约39个字符
    return apiKey.isNotEmpty &&
        apiKey.startsWith('AIza') &&
        apiKey.length >= 35;
  }

  /// 测试网络连接性
  static Future<Map<String, dynamic>> _testNetworkConnectivity() async {
    final result = <String, dynamic>{
      'testName': '网络连接测试',
      'canReachGoogle': false,
      'canReachGoogleApi': false,
      'responseTime': 0,
    };

    try {
      // 测试是否能访问Google
      final startTime = DateTime.now();
      final response = await http
          .get(
            Uri.parse('https://www.google.com'),
          )
          .timeout(const Duration(seconds: 10));

      final endTime = DateTime.now();
      result['responseTime'] = endTime.difference(startTime).inMilliseconds;
      result['canReachGoogle'] = response.statusCode == 200;

      // 测试是否能访问Google API域名
      if (result['canReachGoogle']) {
        try {
          final apiResponse = await http
              .get(
                Uri.parse('https://generativelanguage.googleapis.com'),
              )
              .timeout(const Duration(seconds: 10));
          result['canReachGoogleApi'] = apiResponse.statusCode < 500;
        } catch (e) {
          result['canReachGoogleApi'] = false;
        }
      }
    } catch (e) {
      result['canReachGoogle'] = false;
      result['error'] = e.toString();
    }

    return result;
  }

  /// 测试不同的连接方法
  static Future<List<Map<String, dynamic>>> _testConnectionMethods({
    required String apiKey,
    required String model,
    String? proxyHost,
    int? proxyPort,
  }) async {
    final tests = <Map<String, dynamic>>[];

    // 测试配置列表
    final testConfigs = [
      {
        'name': '直连',
        'useProxy': false,
        'proxyHost': null,
        'proxyPort': null,
      },
      if (proxyHost != null && proxyPort != null)
        {
          'name': '指定代理',
          'useProxy': true,
          'proxyHost': proxyHost,
          'proxyPort': proxyPort,
        },
      // 常见代理端口
      {
        'name': 'Clash(7890)',
        'useProxy': true,
        'proxyHost': '127.0.0.1',
        'proxyPort': 7890
      },
      {
        'name': 'V2Ray(10809)',
        'useProxy': true,
        'proxyHost': '127.0.0.1',
        'proxyPort': 10809
      },
      {
        'name': 'SOCKS(1080)',
        'useProxy': true,
        'proxyHost': '127.0.0.1',
        'proxyPort': 1080
      },
    ];

    for (final config in testConfigs) {
      final testResult = await _testSingleConnection(
        apiKey: apiKey,
        model: model,
        configName: config['name'] as String,
        useProxy: config['useProxy'] as bool,
        proxyHost: config['proxyHost'] as String?,
        proxyPort: config['proxyPort'] as int?,
      );
      tests.add(testResult);
    }

    return tests;
  }

  /// 测试单个连接配置
  static Future<Map<String, dynamic>> _testSingleConnection({
    required String apiKey,
    required String model,
    required String configName,
    required bool useProxy,
    String? proxyHost,
    int? proxyPort,
  }) async {
    final result = <String, dynamic>{
      'testName': configName,
      'success': false,
      'useProxy': useProxy,
      'proxyHost': proxyHost,
      'proxyPort': proxyPort,
      'responseTime': 0,
      'statusCode': 0,
      'error': null,
      'handshakeError': false,
    };

    http.Client? client;

    try {
      // 创建客户端 - 优先使用握手抗性客户端
      if (useProxy && proxyHost != null && proxyPort != null) {
        client = NetworkClient.createHandshakeResistantClient(
          useProxy: true,
          proxyHost: proxyHost,
          proxyPort: proxyPort,
          timeout: const Duration(seconds: 60),
        );
      } else {
        // 对于Google API，使用专门的客户端
        client = NetworkClient.createGoogleApiClient(
          timeout: const Duration(seconds: 60),
        );
      }

      // 构建测试请求
      final url =
          'https://generativelanguage.googleapis.com/v1beta/models/$model:generateContent?key=$apiKey';
      final requestBody = {
        'contents': [
          {
            'parts': [
              {'text': '测试'}
            ]
          }
        ],
        'generationConfig': {
          'maxOutputTokens': 5,
          'temperature': 0.1,
        }
      };

      final startTime = DateTime.now();

      final response = await client
          .post(
            Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode(requestBody),
          )
          .timeout(const Duration(seconds: 60));

      final endTime = DateTime.now();
      result['responseTime'] = endTime.difference(startTime).inMilliseconds;
      result['statusCode'] = response.statusCode;
      result['success'] = response.statusCode == 200;

      if (response.statusCode != 200) {
        result['error'] = 'HTTP ${response.statusCode}: ${response.body}';
      }
    } catch (e) {
      result['error'] = e.toString();
      result['success'] = false;

      // 检查是否是握手异常
      if (e.toString().contains('HandshakeException') ||
          e.toString().contains('Connection terminated during handshake')) {
        result['handshakeError'] = true;
      }
    } finally {
      client?.close();
    }

    return result;
  }

  /// 分析测试结果
  static Map<String, dynamic> _analyzeResults(
      List<Map<String, dynamic>> testResults) {
    final analysis = <String, dynamic>{
      'success': false,
      'workingConfig': null,
      'issues': <String>[],
      'recommendations': <String>[],
    };

    // 查找成功的配置
    final successfulTests =
        testResults.where((test) => test['success'] == true).toList();

    if (successfulTests.isNotEmpty) {
      analysis['success'] = true;
      analysis['workingConfig'] = successfulTests.first;
      analysis['recommendations']
          .add('建议使用${successfulTests.first['testName']}配置');
    } else {
      // 分析失败原因
      final errors = testResults
          .map((test) => test['error'])
          .where((error) => error != null)
          .toList();
      final handshakeErrors =
          testResults.where((test) => test['handshakeError'] == true).toList();

      // 优先检查握手异常
      if (handshakeErrors.isNotEmpty) {
        analysis['issues'].add('SSL/TLS握手失败 - 这是您当前遇到的主要问题');
        analysis['recommendations'].addAll([
          '🔧 立即解决方案：',
          '1. 启动代理软件（Clash、V2Ray、Shadowsocks等）',
          '2. 确保代理软件正常运行（通常在系统托盘可见）',
          '3. 在应用中选择"Gemini代理版"模型',
          '4. 或手动设置代理：127.0.0.1:7890（Clash默认端口）',
          '',
          '🔍 详细排查：',
          '• 检查代理软件是否正确配置Google域名',
          '• 尝试在浏览器中访问 google.com 确认代理工作',
          '• 如果使用企业网络，联系IT部门开放相关端口',
          '• 临时关闭防火墙测试是否为防火墙阻止'
        ]);
      } else {
        // 其他错误类型
        if (errors.any((error) => error.toString().contains('404'))) {
          analysis['issues'].add('API返回404错误，可能是API密钥无效或模型名称错误');
          analysis['recommendations'].add('请检查API密钥是否正确，确认已启用Gemini API');
        }

        if (errors.any((error) => error.toString().contains('timeout'))) {
          analysis['issues'].add('连接超时，网络访问受限');
          analysis['recommendations'].add('建议使用代理或VPN访问Google API');
        }

        if (errors.any((error) => error.toString().contains('403'))) {
          analysis['issues'].add('API访问被拒绝，可能是配额或权限问题');
          analysis['recommendations'].add('请检查API密钥权限和使用配额');
        }

        if (analysis['issues'].isEmpty) {
          analysis['issues'].add('所有连接方式都失败');
          analysis['recommendations'].add('请检查网络连接和API配置');
        }
      }
    }

    return analysis;
  }

  /// 生成诊断报告
  static String generateReport(Map<String, dynamic> diagnosticResults) {
    final buffer = StringBuffer();

    buffer.writeln('=== Google API 连接诊断报告 ===\n');

    if (diagnosticResults['success']) {
      buffer.writeln('✅ 诊断结果：连接成功');
      final workingConfig = diagnosticResults['workingConfig'];
      if (workingConfig != null) {
        buffer.writeln('推荐配置：${workingConfig['testName']}');
        if (workingConfig['useProxy']) {
          buffer.writeln(
              '代理地址：${workingConfig['proxyHost']}:${workingConfig['proxyPort']}');
        }
        buffer.writeln('响应时间：${workingConfig['responseTime']}ms');
      }
    } else {
      buffer.writeln('❌ 诊断结果：连接失败');
    }

    buffer.writeln();

    // 问题列表
    if (diagnosticResults['issues'].isNotEmpty) {
      buffer.writeln('发现的问题：');
      for (final issue in diagnosticResults['issues']) {
        buffer.writeln('• $issue');
      }
      buffer.writeln();
    }

    // 建议列表
    if (diagnosticResults['recommendations'].isNotEmpty) {
      buffer.writeln('建议解决方案：');
      for (final recommendation in diagnosticResults['recommendations']) {
        buffer.writeln('• $recommendation');
      }
      buffer.writeln();
    }

    // 详细测试结果
    buffer.writeln('详细测试结果：');
    for (final test in diagnosticResults['testResults']) {
      final status = test['success'] == true ? '✅' : '❌';
      buffer.writeln('$status ${test['testName']}');
      if (test['responseTime'] != null && test['responseTime'] > 0) {
        buffer.writeln('   响应时间: ${test['responseTime']}ms');
      }
      if (test['error'] != null) {
        buffer.writeln('   错误: ${test['error']}');
      }
    }

    return buffer.toString();
  }
}
