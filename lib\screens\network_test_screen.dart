import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/utils/google_api_diagnostics.dart';
import 'package:novel_app/utils/handshake_fix_helper.dart';
import 'package:novel_app/controllers/api_config_controller.dart';

class NetworkTestScreen extends StatefulWidget {
  const NetworkTestScreen({Key? key}) : super(key: key);

  @override
  State<NetworkTestScreen> createState() => _NetworkTestScreenState();
}

class _NetworkTestScreenState extends State<NetworkTestScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _testResults;
  final _apiKeyController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 尝试从当前配置中获取API密钥
    try {
      final controller = Get.find<ApiConfigController>();
      final currentModel = controller.getCurrentModel();
      if (currentModel.apiFormat == 'Google API' &&
          currentModel.apiKey.isNotEmpty) {
        _apiKeyController.text = currentModel.apiKey;
      }
    } catch (e) {
      // 如果获取失败，保持空白
    }
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    super.dispose();
  }

  Future<void> _runNetworkTest() async {
    setState(() {
      _isLoading = true;
      _testResults = null;
    });

    try {
      final apiKey = _apiKeyController.text.trim();

      Map<String, dynamic> results;
      if (apiKey.isNotEmpty) {
        // 运行Google API诊断
        results = await GoogleApiDiagnostics.diagnoseConnection(
          apiKey: apiKey,
        );
      } else {
        // 运行基本网络测试和自动修复
        results = await HandshakeFixHelper.autoFixHandshakeIssue();
      }

      setState(() {
        _testResults = results;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('测试失败: $e')),
        );
      }
      setState(() {
        _testResults = {
          'success': false,
          'error': e.toString(),
          'recommendations': ['发生错误: $e'],
        };
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildNewTestResults(Map<String, dynamic> results) {
    final success = results['success'] == true;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 总体结果
        Card(
          color: success
              ? Colors.green.withValues(alpha: 0.1)
              : Colors.red.withValues(alpha: 0.1),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: success ? Colors.green : Colors.red,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        success ? '测试成功' : '测试失败',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: success ? Colors.green : Colors.red,
                        ),
                      ),
                      if (results['message'] != null)
                        Text(
                          results['message'],
                          style: const TextStyle(fontSize: 14),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // 代理检测结果
        if (results['proxyDetected'] == true) ...[
          const Card(
            child: ListTile(
              leading: Icon(Icons.vpn_lock, color: Colors.green),
              title: Text('代理检测'),
              subtitle: Text('✅ 检测到可用代理'),
              trailing: Text('127.0.0.1:7890'),
            ),
          ),
          const SizedBox(height: 8),
        ],

        // 工作配置
        if (results['workingConfig'] != null) ...[
          Card(
            child: ListTile(
              leading: const Icon(Icons.settings, color: Colors.blue),
              title: const Text('推荐配置'),
              subtitle: Text('${results['workingConfig']['testName']}'),
            ),
          ),
          const SizedBox(height: 8),
        ],

        // 建议操作
        if (results['recommendations'] != null &&
            (results['recommendations'] as List).isNotEmpty) ...[
          const Text(
            '建议操作',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...((results['recommendations'] as List).map((rec) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('• ',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Expanded(child: Text(rec.toString())),
                          ],
                        ),
                      ))),
                ],
              ),
            ),
          ),
          const SizedBox(height: 8),
        ],

        // 详细报告按钮
        if (!success) ...[
          ElevatedButton.icon(
            onPressed: _showDetailedReport,
            icon: const Icon(Icons.info),
            label: const Text('查看详细报告'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: _runAutoFix,
            icon: const Icon(Icons.build),
            label: const Text('尝试自动修复'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ],
    );
  }

  void _showDetailedReport() {
    if (_testResults == null) return;

    String report;
    if (_apiKeyController.text.trim().isNotEmpty) {
      report = GoogleApiDiagnostics.generateReport(_testResults!);
    } else {
      report = HandshakeFixHelper.generateFixReport(_testResults!);
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('详细诊断报告'),
        content: SingleChildScrollView(
          child: Text(
            report,
            style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Future<void> _runAutoFix() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final fixResults = await HandshakeFixHelper.autoFixHandshakeIssue();

      if (fixResults['success'] == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('修复成功！已自动配置代理设置'),
              backgroundColor: Colors.green,
            ),
          );
        }

        // 重新运行测试
        await _runNetworkTest();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('需要手动处理，请查看建议操作'),
              backgroundColor: Colors.orange,
            ),
          );
        }

        setState(() {
          _testResults = fixResults;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('自动修复失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('网络连接测试'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '网络连接诊断',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '此工具将测试您的网络连接，包括Google API的可达性。'
                      '如果您有Google API密钥，请在下方输入以进行完整测试。',
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _apiKeyController,
                      decoration: const InputDecoration(
                        labelText: 'Google API密钥（可选）',
                        hintText: '输入您的Google API密钥进行完整测试',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _runNetworkTest,
                        child: _isLoading
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  ),
                                  SizedBox(width: 8),
                                  Text('测试中...'),
                                ],
                              )
                            : const Text('开始测试'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_testResults != null) ...[
              const Text(
                '测试结果',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildNewTestResults(_testResults!),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
