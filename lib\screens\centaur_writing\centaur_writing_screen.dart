import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/centaur_writing_controller.dart';

/// 半人马写作主界面
/// 超越人类，超越套路的AI创作思维链
class CentaurWritingScreen extends StatelessWidget {
  const CentaurWritingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CentaurWritingController());

    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.psychology, color: Colors.purple),
            SizedBox(width: 8),
            Text('半人马写作'),
          ],
        ),
        backgroundColor: Colors.purple.shade50,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.resetAll,
            tooltip: '重置所有',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: '帮助',
          ),
        ],
      ),
      body: Column(
        children: [
          // 顶部标语
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.purple.shade50, Colors.white],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: const Column(
              children: [
                Text(
                  '超越人类，超越套路',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'AI创作思维链 · 爽文生成引擎',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),

          // 主要内容区域
          Expanded(
            child: Row(
              children: [
                // 左侧设置面板
                Container(
                  width: 320,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border: Border(
                      right: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: _buildSettingsPanel(controller),
                ),

                // 右侧内容显示区域
                Expanded(
                  child: _buildContentArea(controller),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设置面板
  Widget _buildSettingsPanel(CentaurWritingController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 创作工作流
          _buildWorkflowProgress(controller),
          const SizedBox(height: 20),

          // 基本设置
          _buildBasicSettings(controller),
          const SizedBox(height: 20),

          // 类型选择
          _buildGenreSelector(controller),
          const SizedBox(height: 20),

          // 操作按钮
          _buildActionButtons(controller),
        ],
      ),
    );
  }

  /// 构建工作流进度指示器
  Widget _buildWorkflowProgress(CentaurWritingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '创作工作流',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Obx(() {
          final progress = controller.getWorkflowProgress();
          return Column(
            children: [
              // 进度条
              LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey.shade300,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
              ),
              const SizedBox(height: 4),
              Text(
                '进度: ${(progress * 100).toInt()}%',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],
          );
        }),
        const SizedBox(height: 12),

        // 工作流阶段列表
        Obx(() => Column(
              children: controller.workflowStages.asMap().entries.map((entry) {
                final index = entry.key;
                final stage = entry.value;
                final isCompleted = controller.stageCompleted[index];
                final isSelected = controller.currentMode.value == stage['key'];
                final isCurrent =
                    controller.currentWorkflowStage.value == stage['stage'];

                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: InkWell(
                    onTap: () => controller.switchMode(stage['key']),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? stage['color'].withValues(alpha: 0.1)
                            : (isCompleted
                                ? Colors.green.withValues(alpha: 0.05)
                                : Colors.white),
                        border: Border.all(
                          color: isSelected
                              ? stage['color']
                              : (isCompleted
                                  ? Colors.green
                                  : Colors.grey.shade300),
                          width: isSelected ? 2 : 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          // 阶段状态图标
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: isCompleted
                                  ? Colors.green
                                  : (isCurrent
                                      ? stage['color']
                                      : Colors.grey.shade300),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              isCompleted
                                  ? Icons.check
                                  : (isCurrent
                                      ? stage['icon']
                                      : Icons.radio_button_unchecked),
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 12),

                          // 阶段信息
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      '${stage['stage']}. ${stage['title']}',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: isSelected
                                            ? stage['color']
                                            : Colors.black87,
                                      ),
                                    ),
                                    if (isCompleted) ...[
                                      const SizedBox(width: 8),
                                      Icon(Icons.check_circle,
                                          color: Colors.green, size: 16),
                                    ],
                                  ],
                                ),
                                Text(
                                  stage['description'],
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            )),
      ],
    );
  }

  /// 构建基本设置
  Widget _buildBasicSettings(CentaurWritingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '基本设置',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // 小说标题
        TextField(
          controller: controller.novelTitleController,
          decoration: const InputDecoration(
            labelText: '小说标题',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.title),
          ),
        ),
        const SizedBox(height: 12),

        // 主题
        TextField(
          controller: controller.themeController,
          decoration: const InputDecoration(
            labelText: '小说主题',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.lightbulb_outline),
          ),
        ),
        const SizedBox(height: 12),

        // 背景设定
        TextField(
          controller: controller.backgroundController,
          maxLines: 3,
          decoration: const InputDecoration(
            labelText: '背景设定',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.landscape),
          ),
        ),
        const SizedBox(height: 12),

        // 目标读者
        TextField(
          controller: controller.targetReadersController,
          decoration: const InputDecoration(
            labelText: '目标读者',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.people),
          ),
        ),

        // 自定义提示（用于打脸情节等）
        Obx(() {
          if (controller.currentMode.value == 'face_slap') {
            return Column(
              children: [
                const SizedBox(height: 12),
                TextField(
                  controller: controller.customPromptController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: '当前情况描述',
                    hintText: '描述需要设计打脸情节的具体情况...',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                ),
              ],
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  /// 构建类型选择器
  Widget _buildGenreSelector(CentaurWritingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '小说类型',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Obx(() => Wrap(
              spacing: 8,
              runSpacing: 8,
              children: controller.availableGenres.map((genre) {
                final isSelected = controller.selectedGenres.contains(genre);
                return FilterChip(
                  label: Text(genre),
                  selected: isSelected,
                  onSelected: (selected) => controller.toggleGenre(genre),
                  selectedColor: Colors.purple.withValues(alpha: 0.2),
                  checkmarkColor: Colors.purple,
                );
              }).toList(),
            )),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(CentaurWritingController controller) {
    return Obx(() {
      final currentModeInfo = controller.getCurrentModeInfo();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 完整工作流按钮
          ElevatedButton.icon(
            onPressed: controller.isGenerating.value
                ? null
                : () {
                    controller.executeFullWorkflow();
                  },
            icon: controller.isGenerating.value
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.auto_awesome),
            label: const Text('执行完整工作流'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),

          const SizedBox(height: 8),

          // 当前阶段执行按钮
          ElevatedButton.icon(
            onPressed: controller.isGenerating.value
                ? null
                : () {
                    switch (controller.currentMode.value) {
                      case 'thinking_chain':
                        controller.executeThinkingChain();
                        break;
                      case 'cool_novel':
                        controller.generateCoolNovel();
                        break;
                      case 'anti_routine':
                        controller.analyzeAntiRoutine();
                        break;
                      case 'golden_finger':
                        controller.designGoldenFinger();
                        break;
                      case 'face_slap':
                        controller.designFaceSlap();
                        break;
                    }
                  },
            icon: controller.isGenerating.value
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Icon(currentModeInfo['icon']),
            label: Text(controller.isGenerating.value
                ? '执行中...'
                : '执行${currentModeInfo['title']}'),
            style: ElevatedButton.styleFrom(
              backgroundColor: currentModeInfo['color'],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),

          const SizedBox(height: 8),

          // 继续创作按钮（仅在爽文模式下显示）
          if (controller.currentMode.value == 'cool_novel')
            ElevatedButton.icon(
              onPressed: controller.isGenerating.value ||
                      controller.contentHistory.isEmpty
                  ? null
                  : controller.continueWriting,
              icon: const Icon(Icons.play_arrow),
              label: const Text('继续创作'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),

          const SizedBox(height: 8),

          // 重置工作流按钮
          OutlinedButton.icon(
            onPressed: controller.resetWorkflow,
            icon: const Icon(Icons.refresh),
            label: const Text('重置工作流'),
          ),

          const SizedBox(height: 4),

          // 清空内容按钮
          OutlinedButton.icon(
            onPressed: controller.clearContent,
            icon: const Icon(Icons.clear),
            label: const Text('清空内容'),
          ),
        ],
      );
    });
  }

  /// 构建内容显示区域
  Widget _buildContentArea(CentaurWritingController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              Obx(() {
                final modeInfo = controller.getCurrentModeInfo();
                final progress = controller.getWorkflowProgress();
                return Row(
                  children: [
                    Icon(modeInfo['icon'], color: modeInfo['color']),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          modeInfo['title'],
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (progress > 0)
                          Text(
                            '工作流进度: ${(progress * 100).toInt()}%',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                      ],
                    ),
                  ],
                );
              }),
              const Spacer(),
              // 工作流状态指示器
              Obx(() {
                if (controller.isGenerating.value) {
                  return Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            controller.getCurrentModeInfo()['color'],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '执行中...',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  );
                }
                return const SizedBox.shrink();
              }),
              const SizedBox(width: 8),
              // 复制按钮
              Obx(() => controller.generatedContent.value.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: () {
                        Clipboard.setData(ClipboardData(
                            text: controller.generatedContent.value));
                        Get.snackbar('成功', '内容已复制到剪贴板');
                      },
                      tooltip: '复制内容',
                    )
                  : const SizedBox.shrink()),
            ],
          ),

          const SizedBox(height: 16),

          // 内容显示区域
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Obx(() {
                if (controller.isExecutingFullWorkflow.value ||
                    (controller.isGenerating.value &&
                        controller.workflowOutputHistory.isNotEmpty)) {
                  // 工作流模式显示
                  return Column(
                    children: [
                      // 工作流进度条
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.purple.shade200),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.auto_awesome,
                                  color: Colors.purple,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '半人马写作工作流',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                                const Spacer(),
                                Text(
                                  '${(controller.getWorkflowProgress() * 100).toInt()}%',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            LinearProgressIndicator(
                              value: controller.getWorkflowProgress(),
                              backgroundColor: Colors.grey.shade300,
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                  Colors.purple),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),

                      // 工作流输出历史（带颜色分区）
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: controller.workflowOutputHistory.length,
                            itemBuilder: (context, index) {
                              final output =
                                  controller.workflowOutputHistory[index];
                              return _buildWorkflowOutputItem(output);
                            },
                          ),
                        ),
                      ),
                    ],
                  );
                } else if (controller.isGenerating.value) {
                  // 单独模式执行显示
                  return Column(
                    children: [
                      // 单模式进度条
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Icon(
                                  controller.getCurrentModeInfo()['icon'],
                                  color:
                                      controller.getCurrentModeInfo()['color'],
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '正在执行: ${controller.getCurrentModeInfo()['title']}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            LinearProgressIndicator(
                              backgroundColor: Colors.grey.shade300,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                controller.getCurrentModeInfo()['color'],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),

                      // 内容显示区域
                      Expanded(
                        child: SingleChildScrollView(
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: SelectableText(
                              controller.currentOutput.value.isEmpty
                                  ? '正在生成中，请稍候...'
                                  : controller.currentOutput.value,
                              style: const TextStyle(fontSize: 14, height: 1.6),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                } else if (controller.generatedContent.value.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.psychology,
                            size: 64, color: Colors.grey.shade400),
                        const SizedBox(height: 16),
                        Text(
                          '半人马写作工作流',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '超越人类，超越套路',
                          style: TextStyle(
                            color: Colors.purple,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Column(
                            children: [
                              Text(
                                '请填写左侧设置，然后选择执行方式：',
                                style: TextStyle(
                                  color: Colors.grey.shade700,
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Text(
                                '• 执行完整工作流：一键完成所有创作阶段\n'
                                '• 分步执行：按顺序执行各个创作阶段\n'
                                '• 单独执行：选择特定阶段进行创作',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 12,
                                  height: 1.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                } else {
                  return SingleChildScrollView(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: SelectableText(
                        controller.generatedContent.value,
                        style: const TextStyle(fontSize: 14, height: 1.6),
                      ),
                    ),
                  );
                }
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建工作流输出项（带颜色分区）
  Widget _buildWorkflowOutputItem(Map<String, dynamic> output) {
    final String content = output['content'] ?? '';
    final String type = output['type'] ?? 'info';

    Color backgroundColor;
    Color textColor;
    Color borderColor;
    IconData? icon;

    switch (type) {
      case 'system':
        backgroundColor = Colors.purple.shade50;
        textColor = Colors.purple.shade800;
        borderColor = Colors.purple.shade200;
        icon = Icons.settings;
        break;
      case 'stage':
        backgroundColor = Colors.blue.shade50;
        textColor = Colors.blue.shade800;
        borderColor = Colors.blue.shade200;
        icon = Icons.play_arrow;
        break;
      case 'success':
        backgroundColor = Colors.green.shade50;
        textColor = Colors.green.shade800;
        borderColor = Colors.green.shade200;
        icon = Icons.check_circle;
        break;
      case 'error':
        backgroundColor = Colors.red.shade50;
        textColor = Colors.red.shade800;
        borderColor = Colors.red.shade200;
        icon = Icons.error;
        break;
      case 'warning':
        backgroundColor = Colors.orange.shade50;
        textColor = Colors.orange.shade800;
        borderColor = Colors.orange.shade200;
        icon = Icons.warning;
        break;
      case 'result':
        backgroundColor = Colors.grey.shade50;
        textColor = Colors.grey.shade800;
        borderColor = Colors.grey.shade300;
        icon = Icons.description;
        break;
      case 'stream':
        backgroundColor = Colors.white;
        textColor = Colors.black87;
        borderColor = Colors.transparent;
        icon = null;
        break;
      default: // info
        backgroundColor = Colors.blue.shade50;
        textColor = Colors.blue.shade700;
        borderColor = Colors.blue.shade100;
        icon = Icons.info;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor, width: 1),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null) ...[
            Icon(icon, color: textColor, size: 16),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: SelectableText(
              content,
              style: TextStyle(
                color: textColor,
                fontSize: type == 'stream' ? 14 : 13,
                height: 1.4,
                fontWeight: type == 'stage' || type == 'system'
                    ? FontWeight.bold
                    : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示帮助对话框
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help_outline, color: Colors.purple),
            SizedBox(width: 8),
            Text('半人马写作帮助'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '半人马写作是基于"超越人类，超越套路"理念的AI创作工具，具有以下特色：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text('🧠 思维链分析：AI深度分析创作逻辑和情节发展'),
              SizedBox(height: 8),
              Text('⚡ 爽文创作：基于思维链的高质量爽文生成'),
              SizedBox(height: 8),
              Text('💡 反套路分析：打破常见套路的创新方案'),
              SizedBox(height: 8),
              Text('⭐ 金手指设计：创新的能力系统设计'),
              SizedBox(height: 8),
              Text('🔥 打脸情节：经典打脸情节的精准设计'),
              SizedBox(height: 12),
              Text(
                '使用建议：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('1. 点击"执行完整工作流"一键完成所有创作阶段'),
              Text('2. 或按顺序执行：思维链→反套路→金手指→打脸→爽文'),
              Text('3. 查看工作流进度，了解当前完成状态'),
              Text('4. 使用继续创作功能实现持续创作'),
              Text('5. 重置工作流可以重新开始整个流程'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }
}
