import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/centaur_thinking_chain_service.dart';
import 'package:novel_app/models/novel.dart';

/// 半人马写作控制器
/// 管理AI创作思维链和持续创作功能
class CentaurWritingController extends GetxController {
  final CentaurThinkingChainService _thinkingChainService =
      Get.put(CentaurThinkingChainService());

  // UI控制器
  final TextEditingController novelTitleController = TextEditingController();
  final TextEditingController themeController = TextEditingController();
  final TextEditingController backgroundController = TextEditingController();
  final TextEditingController targetReadersController = TextEditingController();
  final TextEditingController customPromptController = TextEditingController();

  // 响应式状态
  final RxList<String> selectedGenres = <String>[].obs;
  final RxString currentMode = 'thinking_chain'
      .obs; // thinking_chain, cool_novel, anti_routine, golden_finger, face_slap
  final RxBool isGenerating = false.obs;
  final RxString generatedContent = ''.obs;
  final RxString currentOutput = ''.obs;
  final RxList<String> contentHistory = <String>[].obs;
  final RxInt currentWorkflowStage = 1.obs; // 当前工作流阶段 1-5
  final RxBool isExecutingFullWorkflow = false.obs; // 是否正在执行完整工作流

  // 工作流结果存储
  final RxString thinkingChainResult = ''.obs;
  final RxString antiRoutineResult = ''.obs;
  final RxString goldenFingerResult = ''.obs;
  final RxString faceSlapResult = ''.obs;
  final RxList<bool> stageCompleted =
      <bool>[false, false, false, false, false].obs;

  // 工作流输出历史（带颜色标记）
  final RxList<Map<String, dynamic>> workflowOutputHistory =
      <Map<String, dynamic>>[].obs;

  // 可选类型
  final List<String> availableGenres = [
    '玄幻',
    '都市',
    '历史',
    '科幻',
    '武侠',
    '仙侠',
    '军事',
    '游戏',
    '竞技',
    '悬疑',
    '灵异',
    '二次元',
    '轻小说',
    '现实',
    '其他'
  ];

  // 创作工作流阶段
  final List<Map<String, dynamic>> workflowStages = [
    {
      'key': 'thinking_chain',
      'title': '思维链分析',
      'subtitle': 'AI深度思考创作逻辑',
      'icon': Icons.psychology,
      'color': Colors.purple,
      'stage': 1,
      'description': '分析情节逻辑、人物发展、爽文元素设计',
    },
    {
      'key': 'anti_routine',
      'title': '反套路分析',
      'subtitle': '识别并突破常见套路',
      'icon': Icons.lightbulb,
      'color': Colors.green,
      'stage': 2,
      'description': '分析目标类型的套路，提供创新方案',
    },
    {
      'key': 'golden_finger',
      'title': '金手指设计',
      'subtitle': '设计核心能力系统',
      'icon': Icons.star,
      'color': Colors.amber,
      'stage': 3,
      'description': '创建完整的能力系统和升级路径',
    },
    {
      'key': 'face_slap',
      'title': '打脸情节设计',
      'subtitle': '设计关键爽点场景',
      'icon': Icons.flash_on,
      'color': Colors.red,
      'stage': 4,
      'description': '设计"断言-潜藏-揭示-升格"结构',
    },
    {
      'key': 'cool_novel',
      'title': '爽文创作',
      'subtitle': '综合所有元素生成内容',
      'icon': Icons.auto_awesome,
      'color': Colors.orange,
      'stage': 5,
      'description': '基于前面所有分析结果生成爽文内容',
    },
  ];

  @override
  void onInit() {
    super.onInit();
    // 初始化默认值
    targetReadersController.text = '网文读者';
    themeController.text = '成长与逆袭';
  }

  @override
  void onClose() {
    novelTitleController.dispose();
    themeController.dispose();
    backgroundController.dispose();
    targetReadersController.dispose();
    customPromptController.dispose();
    super.onClose();
  }

  /// 切换创作模式
  void switchMode(String mode) {
    currentMode.value = mode;
    generatedContent.value = '';
    currentOutput.value = '';
  }

  /// 切换类型选择
  void toggleGenre(String genre) {
    if (selectedGenres.contains(genre)) {
      selectedGenres.remove(genre);
    } else {
      selectedGenres.add(genre);
    }
  }

  /// 执行工作流阶段
  Future<void> executeWorkflowStage(String stageKey) async {
    switch (stageKey) {
      case 'thinking_chain':
        await executeThinkingChain();
        break;
      case 'anti_routine':
        await analyzeAntiRoutine();
        break;
      case 'golden_finger':
        await designGoldenFinger();
        break;
      case 'face_slap':
        await designFaceSlap();
        break;
      case 'cool_novel':
        await generateCoolNovel();
        break;
    }
  }

  /// 执行完整工作流
  Future<void> executeFullWorkflow() async {
    if (!_validateInput()) return;

    try {
      isGenerating.value = true;
      isExecutingFullWorkflow.value = true;
      workflowOutputHistory.clear();

      // 显示工作流开始信息
      _addWorkflowOutput('🚀 开始执行完整工作流...', 'system');
      _addWorkflowOutput(
          '准备执行五个创作阶段：思维链分析 → 反套路分析 → 金手指设计 → 打脸情节设计 → 爽文创作', 'info');

      // 1. 思维链分析
      _addWorkflowOutput('\n📍 第1步：思维链分析', 'stage');
      await _executeThinkingChainInWorkflow();
      if (thinkingChainResult.value.isEmpty) return;

      // 2. 反套路分析
      _addWorkflowOutput('\n📍 第2步：反套路分析', 'stage');
      await _executeAntiRoutineInWorkflow();
      if (antiRoutineResult.value.isEmpty) return;

      // 3. 金手指设计
      _addWorkflowOutput('\n📍 第3步：金手指设计', 'stage');
      await _executeGoldenFingerInWorkflow();
      if (goldenFingerResult.value.isEmpty) return;

      // 4. 打脸情节设计
      _addWorkflowOutput('\n📍 第4步：打脸情节设计', 'stage');
      if (customPromptController.text.isNotEmpty) {
        await _executeFaceSlapInWorkflow();
      } else {
        _addWorkflowOutput('⏭️ 跳过打脸情节设计（未提供情况描述）', 'warning');
        stageCompleted[3] = true;
      }

      // 5. 综合生成爽文内容
      _addWorkflowOutput('\n📍 第5步：综合爽文创作', 'stage');
      await _executeCoolNovelInWorkflow();

      _addWorkflowOutput('\n🎉 完整工作流执行完成！', 'success');
      _addWorkflowOutput('所有创作阶段已完成，内容已保存到历史记录中。', 'info');

      Get.snackbar('成功', '完整工作流执行完成！',
          backgroundColor: Colors.green.withValues(alpha: 0.8));
    } catch (e) {
      _addWorkflowOutput('\n❌ 工作流执行失败: $e', 'error');
      Get.snackbar('错误', '工作流执行失败: $e',
          backgroundColor: Colors.red.withValues(alpha: 0.8));
    } finally {
      isGenerating.value = false;
      isExecutingFullWorkflow.value = false;
    }
  }

  /// 添加工作流输出（带颜色标记）
  void _addWorkflowOutput(String content, String type) {
    workflowOutputHistory.add({
      'content': content,
      'type': type, // system, info, stage, success, error, warning, result
      'timestamp': DateTime.now(),
    });

    // 更新当前输出显示
    _updateCurrentOutputFromHistory();
  }

  /// 从历史记录更新当前输出显示
  void _updateCurrentOutputFromHistory() {
    final buffer = StringBuffer();
    for (final output in workflowOutputHistory) {
      buffer.writeln(output['content']);
    }
    currentOutput.value = buffer.toString();
  }

  /// 工作流中执行思维链分析
  Future<void> _executeThinkingChainInWorkflow() async {
    try {
      currentWorkflowStage.value = 1;
      _addWorkflowOutput('🧠 开始思维链分析...', 'info');
      _addWorkflowOutput('正在深度分析创作逻辑、人物发展和爽文元素设计...', 'info');

      final result = await _thinkingChainService.executeThinkingChain(
        task: '创作思维链分析',
        novelTitle: novelTitleController.text,
        genres: selectedGenres,
        theme: themeController.text,
        targetReaders: targetReadersController.text,
        background: backgroundController.text,
        previousContent: contentHistory.isNotEmpty ? contentHistory.last : '',
      );

      thinkingChainResult.value = result;
      stageCompleted[0] = true;

      _addWorkflowOutput('✅ 思维链分析完成', 'success');
      _addWorkflowOutput('--- 思维链分析结果 ---', 'result');
      _addWorkflowOutput(result, 'result');
      _addWorkflowOutput('--- 结果结束 ---\n', 'result');
    } catch (e) {
      _addWorkflowOutput('❌ 思维链分析失败: $e', 'error');
      rethrow;
    }
  }

  /// 工作流中执行反套路分析
  Future<void> _executeAntiRoutineInWorkflow() async {
    try {
      currentWorkflowStage.value = 2;
      _addWorkflowOutput('💡 开始反套路分析...', 'info');
      _addWorkflowOutput(
          '正在分析${selectedGenres.first}类型的常见套路，寻找创新突破点...', 'info');

      final result = await _thinkingChainService.analyzeAntiRoutine(
        targetGenre: selectedGenres.first,
      );

      antiRoutineResult.value = result;
      stageCompleted[1] = true;

      _addWorkflowOutput('✅ 反套路分析完成', 'success');
      _addWorkflowOutput('--- 反套路分析结果 ---', 'result');
      _addWorkflowOutput(result, 'result');
      _addWorkflowOutput('--- 结果结束 ---\n', 'result');
    } catch (e) {
      _addWorkflowOutput('❌ 反套路分析失败: $e', 'error');
      rethrow;
    }
  }

  /// 工作流中执行金手指设计
  Future<void> _executeGoldenFingerInWorkflow() async {
    try {
      currentWorkflowStage.value = 3;
      _addWorkflowOutput('⭐ 开始设计金手指系统...', 'info');
      _addWorkflowOutput('正在创建完整的能力系统，包含获取条件、升级路径和使用代价...', 'info');

      final result = await _thinkingChainService.designGoldenFingerSystem(
        novelTitle: novelTitleController.text,
        genres: selectedGenres,
        theme: themeController.text,
        background: backgroundController.text,
      );

      goldenFingerResult.value = result;
      stageCompleted[2] = true;

      _addWorkflowOutput('✅ 金手指系统设计完成', 'success');
      _addWorkflowOutput('--- 金手指系统设计结果 ---', 'result');
      _addWorkflowOutput(result, 'result');
      _addWorkflowOutput('--- 结果结束 ---\n', 'result');
    } catch (e) {
      _addWorkflowOutput('❌ 金手指系统设计失败: $e', 'error');
      rethrow;
    }
  }

  /// 工作流中执行打脸情节设计
  Future<void> _executeFaceSlapInWorkflow() async {
    try {
      currentWorkflowStage.value = 4;
      _addWorkflowOutput('🔥 开始设计打脸情节...', 'info');
      _addWorkflowOutput('正在设计"断言-潜藏-揭示-升格"四步打脸结构...', 'info');

      final result = await _thinkingChainService.designFaceSlapPlot(
        novelTitle: novelTitleController.text,
        currentSituation: customPromptController.text,
        protagonistStatus: '待分析',
        antagonist: '待分析',
        environment: '待分析',
      );

      faceSlapResult.value = result;
      stageCompleted[3] = true;

      _addWorkflowOutput('✅ 打脸情节设计完成', 'success');
      _addWorkflowOutput('--- 打脸情节设计结果 ---', 'result');
      _addWorkflowOutput(result, 'result');
      _addWorkflowOutput('--- 结果结束 ---\n', 'result');
    } catch (e) {
      _addWorkflowOutput('❌ 打脸情节设计失败: $e', 'error');
      rethrow;
    }
  }

  /// 工作流中执行爽文创作
  Future<void> _executeCoolNovelInWorkflow() async {
    try {
      currentWorkflowStage.value = 5;
      _addWorkflowOutput('⚡ 开始爽文创作...', 'info');
      _addWorkflowOutput('正在综合所有分析结果，生成高质量爽文内容...', 'info');

      // 构建综合的思维链分析结果
      String comprehensiveAnalysis = _buildComprehensiveAnalysis();

      // 使用流式输出
      String streamContent = '';
      await _thinkingChainService.generateCoolNovelContent(
        task: '综合爽文内容生成',
        novelTitle: novelTitleController.text,
        genres: selectedGenres,
        theme: themeController.text,
        targetReaders: targetReadersController.text,
        background: backgroundController.text,
        thinkingChainAnalysis: comprehensiveAnalysis,
        previousContent: contentHistory.isNotEmpty ? contentHistory.last : '',
        wordCount: 3000,
        onProgress: (chunk) {
          // 流式输出，实时更新内容
          streamContent += chunk;
          _addWorkflowOutput(chunk, 'stream');
        },
      );

      // 保存最终结果
      generatedContent.value = streamContent;
      contentHistory.add(streamContent);
      stageCompleted[4] = true;

      _addWorkflowOutput('\n✅ 爽文创作完成', 'success');
      _addWorkflowOutput('内容已保存到历史记录中，可以继续创作或查看完整内容。', 'info');
    } catch (e) {
      _addWorkflowOutput('❌ 爽文创作失败: $e', 'error');
      rethrow;
    }
  }

  /// 执行思维链分析
  Future<void> executeThinkingChain() async {
    if (!_validateInput()) return;

    try {
      if (!isGenerating.value) {
        isGenerating.value = true;
      }
      currentWorkflowStage.value = 1;
      currentOutput.value = '';

      // 显示开始状态
      currentOutput.value = '🧠 开始思维链分析...\n正在深度分析创作逻辑、人物发展和爽文元素设计...';
      generatedContent.value = currentOutput.value;

      final result = await _thinkingChainService.executeThinkingChain(
        task: '创作思维链分析',
        novelTitle: novelTitleController.text,
        genres: selectedGenres,
        theme: themeController.text,
        targetReaders: targetReadersController.text,
        background: backgroundController.text,
        previousContent: contentHistory.isNotEmpty ? contentHistory.last : '',
      );

      thinkingChainResult.value = result;
      generatedContent.value = result;
      currentOutput.value = result;
      stageCompleted[0] = true;

      Get.snackbar('成功', '思维链分析完成',
          backgroundColor: Colors.green.withValues(alpha: 0.8));
    } catch (e) {
      Get.snackbar('错误', '思维链分析失败: $e',
          backgroundColor: Colors.red.withValues(alpha: 0.8));
    } finally {
      if (currentMode.value == 'thinking_chain') {
        isGenerating.value = false;
      }
    }
  }

  /// 生成爽文内容
  Future<void> generateCoolNovel() async {
    if (!_validateInput()) return;

    // 如果没有思维链分析结果，先执行分析
    if (thinkingChainResult.value.isEmpty) {
      await executeThinkingChain();
      if (thinkingChainResult.value.isEmpty) return;
    }

    try {
      if (!isGenerating.value) {
        isGenerating.value = true;
      }
      currentWorkflowStage.value = 5;
      currentOutput.value = '';

      // 显示开始状态
      currentOutput.value = '⚡ 开始爽文创作...\n正在综合所有分析结果，生成高质量爽文内容...\n\n';
      generatedContent.value = currentOutput.value;

      // 构建综合的思维链分析结果
      String comprehensiveAnalysis = _buildComprehensiveAnalysis();

      await _thinkingChainService.generateCoolNovelContent(
        task: '综合爽文内容生成',
        novelTitle: novelTitleController.text,
        genres: selectedGenres,
        theme: themeController.text,
        targetReaders: targetReadersController.text,
        background: backgroundController.text,
        thinkingChainAnalysis: comprehensiveAnalysis,
        previousContent: contentHistory.isNotEmpty ? contentHistory.last : '',
        wordCount: 3000,
        onProgress: (chunk) {
          // 流式输出，实时更新内容
          currentOutput.value += chunk;
          generatedContent.value = currentOutput.value;
        },
      );

      contentHistory.add(currentOutput.value);
      stageCompleted[4] = true;

      Get.snackbar('成功', '爽文内容生成完成',
          backgroundColor: Colors.green.withValues(alpha: 0.8));
    } catch (e) {
      Get.snackbar('错误', '爽文内容生成失败: $e',
          backgroundColor: Colors.red.withValues(alpha: 0.8));
    } finally {
      if (currentMode.value == 'cool_novel') {
        isGenerating.value = false;
      }
    }
  }

  /// 分析反套路
  Future<void> analyzeAntiRoutine() async {
    if (selectedGenres.isEmpty) {
      Get.snackbar('提示', '请先选择小说类型',
          backgroundColor: Colors.orange.withValues(alpha: 0.8));
      return;
    }

    try {
      if (!isGenerating.value) {
        isGenerating.value = true;
      }
      currentWorkflowStage.value = 2;
      currentOutput.value = '';

      // 显示开始状态
      currentOutput.value =
          '💡 开始反套路分析...\n正在分析${selectedGenres.first}类型的常见套路，寻找创新突破点...';
      generatedContent.value = currentOutput.value;

      final result = await _thinkingChainService.analyzeAntiRoutine(
        targetGenre: selectedGenres.first,
      );

      antiRoutineResult.value = result;
      generatedContent.value = result;
      currentOutput.value = result;
      stageCompleted[1] = true;

      Get.snackbar('成功', '反套路分析完成',
          backgroundColor: Colors.green.withValues(alpha: 0.8));
    } catch (e) {
      Get.snackbar('错误', '反套路分析失败: $e',
          backgroundColor: Colors.red.withValues(alpha: 0.8));
    } finally {
      if (currentMode.value == 'anti_routine') {
        isGenerating.value = false;
      }
    }
  }

  /// 设计金手指系统
  Future<void> designGoldenFinger() async {
    if (!_validateInput()) return;

    try {
      if (!isGenerating.value) {
        isGenerating.value = true;
      }
      currentWorkflowStage.value = 3;
      currentOutput.value = '';

      // 显示开始状态
      currentOutput.value = '⭐ 开始设计金手指系统...\n正在创建完整的能力系统，包含获取条件、升级路径和使用代价...';
      generatedContent.value = currentOutput.value;

      final result = await _thinkingChainService.designGoldenFingerSystem(
        novelTitle: novelTitleController.text,
        genres: selectedGenres,
        theme: themeController.text,
        background: backgroundController.text,
      );

      goldenFingerResult.value = result;
      generatedContent.value = result;
      currentOutput.value = result;
      stageCompleted[2] = true;

      Get.snackbar('成功', '金手指系统设计完成',
          backgroundColor: Colors.green.withValues(alpha: 0.8));
    } catch (e) {
      Get.snackbar('错误', '金手指系统设计失败: $e',
          backgroundColor: Colors.red.withValues(alpha: 0.8));
    } finally {
      if (currentMode.value == 'golden_finger') {
        isGenerating.value = false;
      }
    }
  }

  /// 设计打脸情节
  Future<void> designFaceSlap() async {
    if (novelTitleController.text.isEmpty ||
        customPromptController.text.isEmpty) {
      Get.snackbar('提示', '请填写小说标题和当前情况描述',
          backgroundColor: Colors.orange.withValues(alpha: 0.8));
      return;
    }

    try {
      if (!isGenerating.value) {
        isGenerating.value = true;
      }
      currentWorkflowStage.value = 4;
      currentOutput.value = '';

      // 显示开始状态
      currentOutput.value = '🔥 开始设计打脸情节...\n正在设计"断言-潜藏-揭示-升格"四步打脸结构...';
      generatedContent.value = currentOutput.value;

      final result = await _thinkingChainService.designFaceSlapPlot(
        novelTitle: novelTitleController.text,
        currentSituation: customPromptController.text,
        protagonistStatus: '待分析',
        antagonist: '待分析',
        environment: '待分析',
      );

      faceSlapResult.value = result;
      generatedContent.value = result;
      currentOutput.value = result;
      stageCompleted[3] = true;

      Get.snackbar('成功', '打脸情节设计完成',
          backgroundColor: Colors.green.withValues(alpha: 0.8));
    } catch (e) {
      Get.snackbar('错误', '打脸情节设计失败: $e',
          backgroundColor: Colors.red.withValues(alpha: 0.8));
    } finally {
      if (currentMode.value == 'face_slap') {
        isGenerating.value = false;
      }
    }
  }

  /// 继续创作
  Future<void> continueWriting() async {
    if (contentHistory.isEmpty) {
      Get.snackbar('提示', '没有可继续的内容',
          backgroundColor: Colors.orange.withOpacity(0.8));
      return;
    }

    await generateCoolNovel();
  }

  /// 清空内容
  void clearContent() {
    generatedContent.value = '';
    currentOutput.value = '';
    thinkingChainResult.value = '';
    contentHistory.clear();
    currentWorkflowStage.value = 1;
  }

  /// 重置所有设置
  void resetAll() {
    novelTitleController.clear();
    themeController.clear();
    backgroundController.clear();
    targetReadersController.text = '网文读者';
    customPromptController.clear();
    selectedGenres.clear();
    clearContent();
  }

  /// 验证输入
  bool _validateInput() {
    if (novelTitleController.text.isEmpty) {
      Get.snackbar('提示', '请输入小说标题',
          backgroundColor: Colors.orange.withOpacity(0.8));
      return false;
    }
    if (selectedGenres.isEmpty) {
      Get.snackbar('提示', '请选择至少一个小说类型',
          backgroundColor: Colors.orange.withOpacity(0.8));
      return false;
    }
    if (themeController.text.isEmpty) {
      Get.snackbar('提示', '请输入小说主题',
          backgroundColor: Colors.orange.withOpacity(0.8));
      return false;
    }
    return true;
  }

  /// 构建综合分析结果
  String _buildComprehensiveAnalysis() {
    final buffer = StringBuffer();

    buffer.writeln('# 半人马写作综合分析报告\n');

    // 思维链分析
    if (thinkingChainResult.value.isNotEmpty) {
      buffer.writeln('## 1. 创作思维链分析');
      buffer.writeln(thinkingChainResult.value);
      buffer.writeln('\n---\n');
    }

    // 反套路分析
    if (antiRoutineResult.value.isNotEmpty) {
      buffer.writeln('## 2. 反套路创新分析');
      buffer.writeln(antiRoutineResult.value);
      buffer.writeln('\n---\n');
    }

    // 金手指设计
    if (goldenFingerResult.value.isNotEmpty) {
      buffer.writeln('## 3. 金手指系统设计');
      buffer.writeln(goldenFingerResult.value);
      buffer.writeln('\n---\n');
    }

    // 打脸情节设计
    if (faceSlapResult.value.isNotEmpty) {
      buffer.writeln('## 4. 打脸情节设计');
      buffer.writeln(faceSlapResult.value);
      buffer.writeln('\n---\n');
    }

    buffer.writeln('## 5. 综合创作指导');
    buffer.writeln('请基于以上所有分析结果，创作一个融合了思维链分析、反套路创新、金手指系统和打脸情节的高质量爽文内容。');
    buffer.writeln('确保内容具有强烈的爽感、创新性和逻辑性。');

    return buffer.toString();
  }

  /// 获取工作流进度
  double getWorkflowProgress() {
    int completedStages = stageCompleted.where((completed) => completed).length;
    return completedStages / stageCompleted.length;
  }

  /// 获取下一个建议执行的阶段
  String? getNextSuggestedStage() {
    for (int i = 0; i < stageCompleted.length; i++) {
      if (!stageCompleted[i]) {
        return workflowStages[i]['key'];
      }
    }
    return null; // 所有阶段都已完成
  }

  /// 重置工作流状态
  void resetWorkflow() {
    for (int i = 0; i < stageCompleted.length; i++) {
      stageCompleted[i] = false;
    }
    thinkingChainResult.value = '';
    antiRoutineResult.value = '';
    goldenFingerResult.value = '';
    faceSlapResult.value = '';
    currentWorkflowStage.value = 1;
  }

  /// 获取当前模式信息
  Map<String, dynamic> getCurrentModeInfo() {
    return workflowStages.firstWhere(
      (mode) => mode['key'] == currentMode.value,
      orElse: () => workflowStages.first,
    );
  }
}
