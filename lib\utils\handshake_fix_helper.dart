import 'dart:io';
import 'package:get/get.dart';
import '../controllers/api_config_controller.dart';
import 'network_client.dart';

/// 握手异常修复助手
class HandshakeFixHelper {
  /// 自动修复握手问题
  static Future<Map<String, dynamic>> autoFixHandshakeIssue() async {
    final result = <String, dynamic>{
      'success': false,
      'steps': <String>[],
      'recommendations': <String>[],
      'proxyDetected': false,
      'fixApplied': false,
    };

    try {
      // 1. 检测代理软件
      result['steps'].add('🔍 检测代理软件...');
      final proxyDetection = await _detectProxyServices();
      result['proxyDetected'] = proxyDetection['hasWorkingProxy'];
      
      if (proxyDetection['hasWorkingProxy']) {
        result['steps'].add('✅ 检测到可用代理: ${proxyDetection['workingProxy']}');
        
        // 2. 自动配置Gemini模型使用代理
        final configResult = await _autoConfigureGeminiProxy(proxyDetection['workingProxy']);
        if (configResult['success']) {
          result['steps'].add('✅ 已自动配置Gemini使用代理');
          result['fixApplied'] = true;
          result['success'] = true;
        } else {
          result['steps'].add('❌ 自动配置失败: ${configResult['error']}');
        }
      } else {
        result['steps'].add('❌ 未检测到可用代理');
        result['recommendations'].addAll([
          '请启动代理软件（Clash、V2Ray、Shadowsocks等）',
          '确保代理软件正常运行并监听常用端口',
          '手动选择"Gemini代理版"模型',
        ]);
      }

      // 3. 提供详细建议
      result['recommendations'].addAll(_getDetailedRecommendations());

    } catch (e) {
      result['steps'].add('❌ 修复过程出错: $e');
    }

    return result;
  }

  /// 检测代理服务
  static Future<Map<String, dynamic>> _detectProxyServices() async {
    final result = <String, dynamic>{
      'hasWorkingProxy': false,
      'workingProxy': null,
      'detectedProxies': <String>[],
    };

    // 常见代理端口
    final proxyPorts = [
      {'name': 'Clash', 'port': 7890},
      {'name': 'Clash备用', 'port': 7891},
      {'name': 'V2Ray', 'port': 10809},
      {'name': 'V2Ray备用', 'port': 10808},
      {'name': 'Shadowsocks', 'port': 1080},
      {'name': 'HTTP代理', 'port': 8080},
    ];

    for (final proxy in proxyPorts) {
      try {
        final socket = await Socket.connect('127.0.0.1', proxy['port'])
            .timeout(const Duration(seconds: 2));
        socket.destroy();
        
        final proxyInfo = '${proxy['name']}(${proxy['port']})';
        result['detectedProxies'].add(proxyInfo);
        
        if (!result['hasWorkingProxy']) {
          result['hasWorkingProxy'] = true;
          result['workingProxy'] = '127.0.0.1:${proxy['port']}';
        }
      } catch (e) {
        // 端口不可用，继续检测下一个
      }
    }

    return result;
  }

  /// 自动配置Gemini使用代理
  static Future<Map<String, dynamic>> _autoConfigureGeminiProxy(String proxyUrl) async {
    try {
      final apiController = Get.find<ApiConfigController>();
      
      // 查找Gemini模型
      final geminiModel = apiController.models.firstWhere(
        (model) => model.name == 'Gemini' && model.apiFormat == 'Google API',
        orElse: () => throw Exception('未找到Gemini模型'),
      );

      // 更新模型配置使用代理
      await apiController.updateModelConfig(
        geminiModel.name,
        useProxy: true,
        proxyUrl: proxyUrl,
        timeout: 180, // 设置更长的超时时间
      );

      return {
        'success': true,
        'message': '已配置Gemini模型使用代理: $proxyUrl',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 获取详细建议
  static List<String> _getDetailedRecommendations() {
    return [
      '',
      '📋 完整解决方案：',
      '',
      '1️⃣ 代理软件推荐：',
      '• Clash for Windows (推荐新手)',
      '• V2Ray (功能强大)',
      '• Shadowsocks (轻量级)',
      '',
      '2️⃣ 配置步骤：',
      '• 下载并安装代理软件',
      '• 导入订阅链接或配置文件',
      '• 启动代理并选择合适的节点',
      '• 确保系统代理已开启',
      '',
      '3️⃣ 验证方法：',
      '• 浏览器访问 google.com',
      '• 如果能正常访问，代理工作正常',
      '• 在应用中选择"Gemini代理版"模型',
      '',
      '4️⃣ 常见问题：',
      '• 代理软件启动但无法访问Google → 检查节点是否有效',
      '• 应用仍然报错 → 尝试重启应用',
      '• 连接不稳定 → 更换其他节点',
      '',
      '🆘 如果以上方法都无效：',
      '• 尝试使用手机热点',
      '• 联系网络管理员（企业网络）',
      '• 使用其他AI模型（如ChatGPT、Claude等）',
    ];
  }

  /// 测试修复效果
  static Future<Map<String, dynamic>> testFix() async {
    try {
      final apiController = Get.find<ApiConfigController>();
      
      // 查找Gemini模型
      final geminiModel = apiController.models.firstWhere(
        (model) => model.name == 'Gemini' && model.apiFormat == 'Google API',
        orElse: () => throw Exception('未找到Gemini模型'),
      );

      // 测试连接
      final testResult = await apiController.validateModelConnection(geminiModel.name);
      
      return {
        'success': testResult['success'],
        'message': testResult['message'],
        'details': testResult,
      };
    } catch (e) {
      return {
        'success': false,
        'message': '测试失败: $e',
        'error': e.toString(),
      };
    }
  }

  /// 生成修复报告
  static String generateFixReport(Map<String, dynamic> fixResult) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== 握手异常修复报告 ===\n');
    
    if (fixResult['success']) {
      buffer.writeln('✅ 修复成功！');
      if (fixResult['fixApplied']) {
        buffer.writeln('已自动配置代理设置');
      }
    } else {
      buffer.writeln('❌ 需要手动处理');
    }
    
    buffer.writeln('\n执行步骤：');
    for (final step in fixResult['steps']) {
      buffer.writeln('$step');
    }
    
    if (fixResult['recommendations'].isNotEmpty) {
      buffer.writeln('\n建议操作：');
      for (final recommendation in fixResult['recommendations']) {
        buffer.writeln('$recommendation');
      }
    }
    
    return buffer.toString();
  }

  /// 快速诊断握手问题
  static Future<bool> isHandshakeIssue(String errorMessage) async {
    final handshakeKeywords = [
      'HandshakeException',
      'Connection terminated during handshake',
      'SSL handshake',
      'TLS handshake',
      'Certificate verify failed',
      'Connection reset',
    ];

    return handshakeKeywords.any((keyword) => 
        errorMessage.toLowerCase().contains(keyword.toLowerCase()));
  }

  /// 获取快速修复建议
  static List<String> getQuickFixSuggestions() {
    return [
      '🚀 快速修复握手异常：',
      '',
      '1. 启动代理软件（Clash/V2Ray等）',
      '2. 选择"Gemini代理版"模型',
      '3. 或手动设置代理：127.0.0.1:7890',
      '4. 重新测试连接',
      '',
      '如果问题持续，请使用应用内的自动修复功能。',
    ];
  }
}
