import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:flutter/foundation.dart';

/// 网络客户端工具类，确保正确继承系统网络配置
class NetworkClient {
  /// 创建一个能够正确继承系统网络配置的HTTP客户端
  static http.Client createSystemClient({
    Duration? timeout,
    bool enableSystemProxy = true,
  }) {
    if (kIsWeb) {
      // Web平台使用默认客户端
      return http.Client();
    }

    // 创建HttpClient实例
    final httpClient = HttpClient();

    if (enableSystemProxy) {
      // 启用自动重定向
      httpClient.autoUncompress = true;

      // 设置用户代理
      httpClient.userAgent = 'DaiZongAI/1.0 (Windows)';

      // 尝试设置系统代理检测
      try {
        // 检查环境变量中的代理设置
        final httpProxy = Platform.environment['HTTP_PROXY'] ??
            Platform.environment['http_proxy'];
        final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
            Platform.environment['https_proxy'];

        if (httpProxy != null || httpsProxy != null) {
          // 如果环境变量中有代理设置，使用它
          final proxyUrl = httpsProxy ?? httpProxy;
          httpClient.findProxy = (uri) => 'PROXY $proxyUrl';
          print('使用环境变量代理: $proxyUrl');
        } else {
          // 尝试使用系统代理自动检测
          httpClient.findProxy = (uri) {
            // 对于Google服务，优先尝试代理连接
            if (uri.host.contains('google') ||
                uri.host.contains('googleapis.com') ||
                uri.host.contains('generativelanguage.googleapis.com')) {
              // 常见的本地代理端口，按优先级排序
              final commonProxyPorts = [
                '7890', // Clash默认端口
                '7891', // Clash备用端口
                '10809', // V2Ray常用端口
                '10808', // V2Ray备用端口
                '1080', // SOCKS代理常用端口
                '8080', // HTTP代理常用端口
                '8118', // Privoxy默认端口
                '3128', // Squid默认端口
              ];

              // 对于Google API，强制使用代理
              for (final port in commonProxyPorts) {
                return 'PROXY 127.0.0.1:$port';
              }

              // 如果没有找到代理，对于Google服务返回DIRECT可能会失败
              print('警告: 未检测到代理，Google API可能无法访问');
              return 'DIRECT';
            }
            return 'DIRECT';
          };
        }
      } catch (e) {
        print('设置代理检测失败: $e');
        // 如果设置失败，使用直连
      }
    }

    // 设置超时
    if (timeout != null) {
      httpClient.connectionTimeout = timeout;
      httpClient.idleTimeout = timeout;
    }

    // 设置安全上下文以支持所有证书
    httpClient.badCertificateCallback = (cert, host, port) {
      // 对于Google API等知名服务，我们信任其证书
      if (host.contains('googleapis.com') ||
          host.contains('google.com') ||
          host.contains('generativelanguage.googleapis.com')) {
        return true;
      }
      return false;
    };

    return IOClient(httpClient);
  }

  /// 创建一个使用指定代理的HTTP客户端
  static http.Client createProxyClient({
    required String proxyHost,
    required int proxyPort,
    Duration? timeout,
  }) {
    if (kIsWeb) {
      throw UnsupportedError('代理客户端在Web平台不受支持');
    }

    final httpClient = HttpClient();

    // 设置代理
    httpClient.findProxy = (uri) => 'PROXY $proxyHost:$proxyPort';

    // 设置超时
    if (timeout != null) {
      httpClient.connectionTimeout = timeout;
      httpClient.idleTimeout = timeout;
    }

    // 启用自动重定向
    httpClient.autoUncompress = true;

    // 设置用户代理
    httpClient.userAgent = 'DaiZongAI/1.0 (Windows; Proxy)';

    return IOClient(httpClient);
  }

  /// 测试网络连接
  static Future<Map<String, dynamic>> testConnection({
    required String url,
    Duration? timeout,
    bool useProxy = false,
    String? proxyHost,
    int? proxyPort,
  }) async {
    http.Client? client;

    try {
      if (useProxy && proxyHost != null && proxyPort != null) {
        client = createProxyClient(
          proxyHost: proxyHost,
          proxyPort: proxyPort,
          timeout: timeout ?? const Duration(seconds: 30),
        );
      } else {
        client = createSystemClient(
          timeout: timeout ?? const Duration(seconds: 30),
          enableSystemProxy: true,
        );
      }

      final startTime = DateTime.now();

      final response = await client.get(
        Uri.parse(url),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DaiZongAI/1.0 (Connection Test)',
        },
      ).timeout(timeout ?? const Duration(seconds: 30));

      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;

      return {
        'success': response.statusCode < 500,
        'statusCode': response.statusCode,
        'responseTime': responseTime,
        'message': '连接测试成功，状态码: ${response.statusCode}，响应时间: ${responseTime}ms',
        'headers': response.headers,
        'useProxy': useProxy,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': '连接测试失败: ${e.toString()}',
        'useProxy': useProxy,
      };
    } finally {
      client?.close();
    }
  }

  /// 检测系统代理配置
  static Future<Map<String, dynamic>> detectSystemProxy() async {
    if (kIsWeb) {
      return {
        'hasProxy': false,
        'message': 'Web平台无法检测系统代理',
      };
    }

    try {
      final results = <String, dynamic>{
        'hasProxy': false,
        'detectedProxies': <String>[],
        'workingProxies': <String>[],
      };

      // 1. 检测环境变量代理
      final httpProxy = Platform.environment['HTTP_PROXY'] ??
          Platform.environment['http_proxy'];
      final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
          Platform.environment['https_proxy'];

      if (httpProxy != null || httpsProxy != null) {
        final proxyUrl = httpsProxy ?? httpProxy;
        results['detectedProxies'].add('环境变量: $proxyUrl');
        results['hasProxy'] = true;
      }

      // 2. 检测常见的本地代理端口
      final commonProxyPorts = [
        '7890', // Clash默认端口
        '1080', // SOCKS代理常用端口
        '8080', // HTTP代理常用端口
        '10809', // V2Ray常用端口
        '7891', // Clash备用端口
        '10808', // V2Ray备用端口
      ];

      for (final port in commonProxyPorts) {
        try {
          // 尝试连接本地代理端口
          final socket = await Socket.connect('127.0.0.1', int.parse(port))
              .timeout(const Duration(seconds: 1));
          socket.destroy();

          final proxyUrl = '127.0.0.1:$port';
          results['detectedProxies'].add('本地代理: $proxyUrl');
          results['workingProxies'].add(proxyUrl);
          results['hasProxy'] = true;
        } catch (e) {
          // 端口不可用，继续检测下一个
        }
      }

      // 3. 生成检测结果消息
      if (results['hasProxy']) {
        final detectedCount = results['detectedProxies'].length;
        final workingCount = results['workingProxies'].length;
        results['message'] = '检测到 $detectedCount 个代理配置，其中 $workingCount 个可用';
      } else {
        results['message'] = '未检测到可用的代理配置';
      }

      return results;
    } catch (e) {
      return {
        'hasProxy': false,
        'error': e.toString(),
        'message': '检测系统代理时出错: ${e.toString()}',
      };
    }
  }

  /// 创建用于Google API的专用客户端
  static http.Client createGoogleApiClient({
    Duration? timeout,
    bool useProxy = false,
    String? proxyHost,
    int? proxyPort,
  }) {
    if (useProxy && proxyHost != null && proxyPort != null) {
      return createProxyClient(
        proxyHost: proxyHost,
        proxyPort: proxyPort,
        timeout: timeout ?? const Duration(seconds: 120),
      );
    } else {
      return createSystemClient(
        timeout: timeout ?? const Duration(seconds: 120),
        enableSystemProxy: true,
      );
    }
  }

  /// 测试Google API连接
  static Future<Map<String, dynamic>> testGoogleApiConnection({
    required String apiKey,
    String? proxyHost,
    int? proxyPort,
    Duration? timeout,
  }) async {
    final testResults = <String, dynamic>{
      'success': false,
      'attempts': <Map<String, dynamic>>[],
      'workingConfig': null,
    };

    // 测试配置列表
    final testConfigs = [
      {
        'name': '直连',
        'useProxy': false,
        'proxyHost': null,
        'proxyPort': null,
      },
      if (proxyHost != null && proxyPort != null)
        {
          'name': '指定代理',
          'useProxy': true,
          'proxyHost': proxyHost,
          'proxyPort': proxyPort,
        },
      // 常见代理端口测试
      {
        'name': 'Clash代理(7890)',
        'useProxy': true,
        'proxyHost': '127.0.0.1',
        'proxyPort': 7890
      },
      {
        'name': 'Clash代理(7891)',
        'useProxy': true,
        'proxyHost': '127.0.0.1',
        'proxyPort': 7891
      },
      {
        'name': 'V2Ray代理(10809)',
        'useProxy': true,
        'proxyHost': '127.0.0.1',
        'proxyPort': 10809
      },
      {
        'name': 'V2Ray代理(10808)',
        'useProxy': true,
        'proxyHost': '127.0.0.1',
        'proxyPort': 10808
      },
    ];

    for (final config in testConfigs) {
      final attemptResult = await _testSingleGoogleApiConfig(
        apiKey: apiKey,
        configName: config['name'] as String,
        useProxy: config['useProxy'] as bool,
        proxyHost: config['proxyHost'] as String?,
        proxyPort: config['proxyPort'] as int?,
        timeout: timeout ?? const Duration(seconds: 30),
      );

      testResults['attempts'].add(attemptResult);

      if (attemptResult['success']) {
        testResults['success'] = true;
        testResults['workingConfig'] = config;
        testResults['message'] = '找到可用配置: ${config['name']}';
        break;
      }
    }

    if (!testResults['success']) {
      testResults['message'] = '所有配置都无法连接到Google API，请检查网络或代理设置';
    }

    return testResults;
  }

  /// 测试单个Google API配置
  static Future<Map<String, dynamic>> _testSingleGoogleApiConfig({
    required String apiKey,
    required String configName,
    required bool useProxy,
    String? proxyHost,
    int? proxyPort,
    required Duration timeout,
  }) async {
    http.Client? client;

    try {
      // 创建客户端
      if (useProxy && proxyHost != null && proxyPort != null) {
        client = createProxyClient(
          proxyHost: proxyHost,
          proxyPort: proxyPort,
          timeout: timeout,
        );
      } else {
        client = createSystemClient(
          timeout: timeout,
          enableSystemProxy: true,
        );
      }

      // 构建测试请求
      final url =
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=$apiKey';
      final requestBody = {
        'contents': [
          {
            'parts': [
              {'text': '请回答"测试"'}
            ]
          }
        ],
        'generationConfig': {
          'maxOutputTokens': 10,
          'temperature': 0.1,
        }
      };

      final startTime = DateTime.now();

      final response = await client
          .post(
            Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode(requestBody),
          )
          .timeout(timeout);

      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;

      return {
        'success': response.statusCode == 200,
        'configName': configName,
        'statusCode': response.statusCode,
        'responseTime': responseTime,
        'message': response.statusCode == 200
            ? '连接成功，响应时间: ${responseTime}ms'
            : '连接失败，状态码: ${response.statusCode}',
        'useProxy': useProxy,
        'proxyHost': proxyHost,
        'proxyPort': proxyPort,
      };
    } catch (e) {
      return {
        'success': false,
        'configName': configName,
        'error': e.toString(),
        'message': '连接失败: ${e.toString()}',
        'useProxy': useProxy,
        'proxyHost': proxyHost,
        'proxyPort': proxyPort,
      };
    } finally {
      client?.close();
    }
  }
}
