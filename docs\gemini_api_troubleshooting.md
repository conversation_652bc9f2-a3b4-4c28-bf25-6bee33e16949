# Google Gemini API 连接问题解决指南

## 🚨 握手异常 - 最常见问题

### HandshakeException: Connection terminated during handshake

**这是您当前遇到的问题！** 这是SSL/TLS握手失败，通常是网络访问限制导致的。

**🔧 立即解决方案：**

1. **启动代理软件**
   - 确保Clash、V2Ray、Shadowsocks等代理软件正在运行
   - 检查系统托盘是否有代理软件图标

2. **在应用中选择"Gemini代理版"模型**
   - 打开模型设置
   - 选择"Gemini代理版"而不是普通的"Gemini"
   - 这个版本会自动使用代理

3. **手动配置代理（如果自动检测失败）**
   - 在模型设置中启用"使用代理"
   - 设置代理地址：
     - Clash: `127.0.0.1:7890`
     - V2Ray: `127.0.0.1:10809`
     - Shadowsocks: `127.0.0.1:1080`

4. **验证代理工作**
   - 在浏览器中访问 `https://www.google.com`
   - 如果能正常访问，说明代理工作正常

**🔍 详细排查步骤：**

1. **检查代理软件状态**
   ```
   - 代理软件是否启动？
   - 是否显示"已连接"状态？
   - 代理规则是否包含Google域名？
   ```

2. **测试网络连接**
   ```
   - 能否访问百度？（测试基本网络）
   - 能否访问Google？（测试代理）
   - 能否访问YouTube？（测试代理稳定性）
   ```

3. **检查防火墙设置**
   - 临时关闭Windows防火墙测试
   - 确保应用被允许通过防火墙

## 常见问题及解决方案

### 1. 404错误 - API密钥或模型问题

**错误现象：**
- API返回404 Not Found错误
- 提示"API密钥无效或已过期"

**可能原因：**
1. API密钥格式不正确或已过期
2. 模型名称错误
3. API路径配置错误
4. 未启用Gemini API服务

**解决方案：**
1. **检查API密钥：**
   - 确保API密钥以`AIza`开头
   - 长度约39个字符
   - 在Google Cloud Console中确认密钥有效

2. **验证模型名称：**
   - 推荐使用：`gemini-1.5-flash`
   - 其他可用模型：`gemini-1.5-pro`、`gemini-pro`

3. **确认API服务已启用：**
   - 访问 [Google Cloud Console](https://console.cloud.google.com/)
   - 启用 "Generative Language API"

### 2. 超时错误 - 网络连接问题

**错误现象：**
- 连接超时
- 请求无响应
- 网络错误

**可能原因：**
1. 网络无法直接访问Google服务
2. 防火墙阻止连接
3. 需要使用代理

**解决方案：**
1. **使用代理：**
   - 在模型设置中选择"Gemini代理版"
   - 或手动配置代理：
     - Clash: `127.0.0.1:7890`
     - V2Ray: `127.0.0.1:10809`
     - 其他代理软件的相应端口

2. **检查网络连接：**
   - 确保能访问 `https://www.google.com`
   - 测试能否访问 `https://generativelanguage.googleapis.com`

### 3. 403错误 - 权限或配额问题

**错误现象：**
- API访问被拒绝
- 提示权限不足

**可能原因：**
1. API密钥权限不足
2. 超出使用配额
3. 地区限制

**解决方案：**
1. **检查API权限：**
   - 确认API密钥有Generative Language API权限
   - 检查项目配置

2. **查看使用配额：**
   - 在Google Cloud Console中查看API使用情况
   - 确认未超出免费配额或付费限制

## 自动诊断功能

应用内置了Google API自动诊断功能：

1. **在模型设置页面点击"测试连接"**
2. **系统会自动：**
   - 验证API密钥格式
   - 测试网络连接
   - 尝试不同的代理配置
   - 提供详细的诊断报告

3. **根据诊断结果：**
   - 自动推荐最佳配置
   - 显示具体错误原因
   - 提供解决建议

## 推荐配置

### 方案一：直连（适用于海外用户）
```
模型：Gemini
API密钥：您的API密钥
使用代理：否
```

### 方案二：代理连接（适用于国内用户）
```
模型：Gemini代理版
API密钥：您的API密钥
使用代理：是
代理地址：127.0.0.1:7890 (Clash)
```

### 方案三：自定义代理
```
模型：Gemini
API密钥：您的API密钥
使用代理：是
代理地址：您的代理服务器地址:端口
```

## 常用代理软件端口

| 软件 | 默认端口 | 协议 |
|------|----------|------|
| Clash | 7890 | HTTP |
| Clash | 7891 | HTTP (备用) |
| V2Ray | 10809 | HTTP |
| V2Ray | 10808 | HTTP (备用) |
| Shadowsocks | 1080 | SOCKS5 |
| Privoxy | 8118 | HTTP |

## 获取API密钥步骤

1. **访问Google AI Studio：**
   - 打开 [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)

2. **创建API密钥：**
   - 点击"Create API Key"
   - 选择或创建项目
   - 复制生成的API密钥

3. **启用API服务：**
   - 访问 [Google Cloud Console](https://console.cloud.google.com/)
   - 搜索"Generative Language API"
   - 点击启用

## 测试API连接

您可以使用以下curl命令测试API连接：

```bash
curl -X POST \
  'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "contents": [
      {
        "parts": [
          {"text": "Hello"}
        ]
      }
    ]
  }'
```

将`YOUR_API_KEY`替换为您的实际API密钥。

## 联系支持

如果以上方案都无法解决问题，请：

1. 使用应用内的"测试连接"功能获取详细诊断报告
2. 记录具体的错误信息
3. 联系技术支持并提供诊断报告

## 更新日志

- 2024-06-24: 添加自动诊断功能
- 2024-06-24: 优化代理检测逻辑
- 2024-06-24: 增加详细错误提示
